{"version": 3, "sources": ["../../@radix-ui/react-progress/src/progress.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ElementRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAsDf,yBAAA;AA5CR,IAAM,gBAAgB;AACtB,IAAM,cAAc;AAGpB,IAAM,CAAC,uBAAuB,mBAAmB,IAAI,mBAAmB,aAAa;AAIrF,IAAM,CAAC,kBAAkB,kBAAkB,IACzC,sBAA4C,aAAa;AAU3D,IAAM,WAAiB;EACrB,CAAC,OAAmC,iBAAiB;AACnD,UAAM;MACJ;MACA,OAAO,YAAY;MACnB,KAAK;MACL,gBAAgB;MAChB,GAAG;IACL,IAAI;AAEJ,SAAK,WAAW,YAAY,MAAM,CAAC,iBAAiB,OAAO,GAAG;AAC5D,cAAQ,MAAM,mBAAmB,GAAG,OAAO,IAAI,UAAU,CAAC;IAC5D;AAEA,UAAM,MAAM,iBAAiB,OAAO,IAAI,UAAU;AAElD,QAAI,cAAc,QAAQ,CAAC,mBAAmB,WAAW,GAAG,GAAG;AAC7D,cAAQ,MAAM,qBAAqB,GAAG,SAAS,IAAI,UAAU,CAAC;IAChE;AAEA,UAAM,QAAQ,mBAAmB,WAAW,GAAG,IAAI,YAAY;AAC/D,UAAM,aAAa,SAAS,KAAK,IAAI,cAAc,OAAO,GAAG,IAAI;AAEjE,eACE,wBAAC,kBAAA,EAAiB,OAAO,iBAAiB,OAAc,KACtD,cAAA;MAAC,UAAU;MAAV;QACC,iBAAe;QACf,iBAAe;QACf,iBAAe,SAAS,KAAK,IAAI,QAAQ;QACzC,kBAAgB;QAChB,MAAK;QACL,cAAY,iBAAiB,OAAO,GAAG;QACvC,cAAY,SAAS;QACrB,YAAU;QACT,GAAG;QACJ,KAAK;MAAA;IACP,EAAA,CACF;EAEJ;AACF;AAEA,SAAS,cAAc;AAMvB,IAAM,iBAAiB;AAKvB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,iBAAiB,GAAG,eAAe,IAAI;AAC/C,UAAM,UAAU,mBAAmB,gBAAgB,eAAe;AAClE,eACE;MAAC,UAAU;MAAV;QACC,cAAY,iBAAiB,QAAQ,OAAO,QAAQ,GAAG;QACvD,cAAY,QAAQ,SAAS;QAC7B,YAAU,QAAQ;QACjB,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAIhC,SAAS,qBAAqB,OAAe,KAAa;AACxD,SAAO,GAAG,KAAK,MAAO,QAAQ,MAAO,GAAG,CAAC;AAC3C;AAEA,SAAS,iBAAiB,OAAkC,UAAiC;AAC3F,SAAO,SAAS,OAAO,kBAAkB,UAAU,WAAW,aAAa;AAC7E;AAEA,SAAS,SAAS,OAA6B;AAC7C,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,iBAAiB,KAAyB;AAEjD,SACE,SAAS,GAAG,KACZ,CAAC,MAAM,GAAG,KACV,MAAM;AAEV;AAEA,SAAS,mBAAmB,OAAY,KAA8B;AAEpE,SACE,SAAS,KAAK,KACd,CAAC,MAAM,KAAK,KACZ,SAAS,OACT,SAAS;AAEb;AAGA,SAAS,mBAAmB,WAAmB,eAAuB;AACpE,SAAO,mCAAmC,SAAS,oBAAoB,aAAa,yEAAyE,WAAW;AAC1K;AAEA,SAAS,qBAAqB,WAAmB,eAAuB;AACtE,SAAO,qCAAqC,SAAS,oBAAoB,aAAa;;gDAExC,WAAW;;;;AAI3D;AAEA,IAAM,OAAO;AACb,IAAM,YAAY;", "names": []}