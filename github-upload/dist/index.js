var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// server/index.ts
import dotenv2 from "dotenv";
import express2 from "express";
import session from "express-session";
import rateLimit from "express-rate-limit";
import helmet from "helmet";

// server/routes.ts
import { createServer } from "http";

// server/storage.ts
import dotenv from "dotenv";

// shared/schema.ts
var schema_exports = {};
__export(schema_exports, {
  adminLoginSchema: () => adminLoginSchema,
  feedback: () => feedback,
  insertFeedbackSchema: () => insertFeedbackSchema,
  insertProductSchema: () => insertProductSchema,
  insertUserSchema: () => insertUserSchema,
  insertWishlistSchema: () => insertWishlistSchema,
  products: () => products,
  productsRelations: () => productsRelations,
  users: () => users,
  usersRelations: () => usersRelations,
  wishlists: () => wishlists,
  wishlistsRelations: () => wishlistsRelations
});
import { pgTable, text, serial, integer, boolean, timestamp, real } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
var users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  whatsappNumber: text("whatsapp_number").notNull().unique(),
  password: text("password"),
  isAdmin: boolean("is_admin").default(false),
  isPrimaryAdmin: boolean("is_primary_admin").default(false),
  createdAt: timestamp("created_at").defaultNow()
});
var products = pgTable("products", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  code: text("code").notNull().unique(),
  category: text("category").notNull(),
  length: real("length").notNull(),
  // in cm
  breadth: real("breadth").notNull(),
  // in cm
  height: real("height").notNull(),
  // in cm
  finish: text("finish").notNull(),
  material: text("material").default("").notNull(),
  imageUrl: text("image_url"),
  imageUrls: text("image_urls").array(),
  // Multiple images
  description: text("description"),
  // Product description
  status: text("status").default("active"),
  // active, inactive, draft
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var wishlists = pgTable("wishlists", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  productId: integer("product_id").notNull(),
  createdAt: timestamp("created_at").defaultNow()
});
var feedback = pgTable("feedback", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  productId: integer("product_id"),
  customerName: text("customer_name").notNull(),
  customerPhone: text("customer_phone"),
  rating: integer("rating").notNull(),
  // 1-5 stars
  title: text("title").notNull(),
  message: text("message").notNull(),
  isApproved: boolean("is_approved").default(false),
  isPublished: boolean("is_published").default(false),
  adminNotes: text("admin_notes"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var usersRelations = relations(users, ({ many }) => ({
  wishlists: many(wishlists)
}));
var productsRelations = relations(products, ({ many }) => ({
  wishlists: many(wishlists)
}));
var wishlistsRelations = relations(wishlists, ({ one }) => ({
  user: one(users, {
    fields: [wishlists.userId],
    references: [users.id]
  }),
  product: one(products, {
    fields: [wishlists.productId],
    references: [products.id]
  })
}));
var insertUserSchema = createInsertSchema(users).pick({
  name: true,
  whatsappNumber: true
});
var adminLoginSchema = z.object({
  whatsappNumber: z.string().min(1),
  password: z.string().min(6)
});
var insertProductSchema = createInsertSchema(products).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});
var insertWishlistSchema = createInsertSchema(wishlists).omit({
  id: true,
  createdAt: true
});
var insertFeedbackSchema = createInsertSchema(feedback).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

// server/db.ts
import pkg from "pg";
import { drizzle } from "drizzle-orm/node-postgres";
var { Pool } = pkg;
var isDev = process.env.NODE_ENV === "development";
var pool = null;
var db;
if (isDev && !process.env.DATABASE_URL) {
  console.warn("Running with mock database for local development");
  pool = null;
  db = {
    query: async () => [],
    select: () => ({ from: () => [] }),
    insert: () => ({ values: () => ({ returning: () => [] }) }),
    delete: () => ({ where: () => ({ returning: () => [] }) }),
    update: () => ({ set: () => ({ where: () => ({ returning: () => [] }) }) })
  };
} else {
  if (!process.env.DATABASE_URL) {
    throw new Error(
      "DATABASE_URL must be set. Did you forget to provision a database?"
    );
  }
  pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = drizzle({ client: pool, schema: schema_exports });
}

// server/storage.ts
import { eq, or, sql, and, desc, asc } from "drizzle-orm";
dotenv.config();
var MemStorage = class {
  users;
  products;
  wishlists;
  currentUserId;
  currentProductId;
  currentWishlistId;
  constructor() {
    this.users = /* @__PURE__ */ new Map();
    this.products = /* @__PURE__ */ new Map();
    this.wishlists = /* @__PURE__ */ new Map();
    this.currentUserId = 1;
    this.currentProductId = 1;
    this.currentWishlistId = 1;
    this.initializeSampleData();
  }
  initializeSampleData() {
    const adminWhatsApp = "+918882636296";
    console.log("Creating admin user with WhatsApp:", adminWhatsApp);
    const adminUser = {
      id: this.currentUserId++,
      name: "Admin User",
      whatsappNumber: adminWhatsApp,
      password: null,
      isAdmin: true,
      isPrimaryAdmin: true,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.users.set(adminUser.id, adminUser);
    console.log("Admin user created:", { id: adminUser.id, whatsappNumber: adminUser.whatsappNumber });
    const sampleProducts = [
      {
        name: "Handcrafted Brass Ganesha Idol",
        code: "VF-BG-001",
        category: "Brass Idols",
        length: 15,
        breadth: 12,
        height: 20,
        finish: "Antique Brass",
        material: "Pure Brass",
        imageUrl: "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        imageUrls: [
          "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
          "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80",
          "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=90"
        ],
        description: "Exquisitely handcrafted brass Ganesha idol with intricate detailing. Perfect for home temples and spiritual spaces. Made by skilled artisans using traditional techniques."
      },
      {
        name: "Decorative Brass Bowl Set",
        code: "VF-BB-002",
        category: "Home Decor",
        length: 25,
        breadth: 25,
        height: 8,
        finish: "Polished Brass",
        material: "Pure Brass",
        imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        imageUrls: [
          "https://images.unsplash.com/photo-1549497538-303791108f95?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
          "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80"
        ],
        description: "Set of 3 decorative brass bowls with traditional engravings. Ideal for serving dry fruits, sweets, or as decorative pieces."
      },
      {
        name: "Brass Diya Oil Lamp",
        code: "VF-DL-003",
        category: "Lighting",
        length: 10,
        breadth: 10,
        height: 5,
        finish: "Traditional Brass",
        material: "Pure Brass",
        imageUrl: "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        imageUrls: [
          "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
          "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=85"
        ],
        description: "Traditional brass diya perfect for festivals and daily prayers. Handcrafted with beautiful patterns and smooth finish."
      },
      {
        name: "Ornate Brass Kalash",
        code: "VF-BK-004",
        category: "Religious Items",
        length: 12,
        breadth: 12,
        height: 18,
        finish: "Engraved Brass",
        material: "Pure Brass",
        imageUrl: "https://images.unsplash.com/photo-1549497538-303791108f95?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        imageUrls: [
          "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
          "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80",
          "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=85"
        ],
        description: "Sacred brass kalash with intricate engravings. Essential for religious ceremonies and puja rituals. Comes with detailed craftsmanship."
      },
      {
        name: "Conference Table",
        code: "VF-CT-005",
        category: "Tables",
        length: 300,
        breadth: 120,
        height: 75,
        finish: "Mahogany",
        material: "Mahogany Wood",
        imageUrl: "https://images.unsplash.com/photo-1549497538-303791108f95?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
      },
      {
        name: "Modular Bookshelf",
        code: "VF-BS-006",
        category: "Storage",
        length: 100,
        breadth: 30,
        height: 200,
        finish: "White Oak",
        material: "White Oak & Metal",
        imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
      },
      {
        name: "Premium Lounge Chair",
        code: "VF-LC-007",
        category: "Chairs",
        length: 80,
        breadth: 85,
        height: 95,
        finish: "Gray Fabric",
        material: "Premium Fabric & Wood",
        imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
      },
      {
        name: "Modern Side Table",
        code: "VF-ST-008",
        category: "Tables",
        length: 45,
        breadth: 45,
        height: 55,
        finish: "Wood & Metal",
        material: "Engineered Wood & Steel",
        imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
      }
    ];
    sampleProducts.forEach((product) => {
      const newProduct = {
        ...product,
        id: this.currentProductId++,
        material: product.material || "",
        imageUrl: product.imageUrl || null,
        imageUrls: product.imageUrls || null,
        description: product.description || null,
        status: product.status || null,
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      this.products.set(newProduct.id, newProduct);
    });
  }
  async getUser(id) {
    return this.users.get(id);
  }
  async getUserByWhatsApp(whatsappNumber) {
    return Array.from(this.users.values()).find(
      (user) => user.whatsappNumber === whatsappNumber
    );
  }
  async createUser(insertUser) {
    const id = this.currentUserId++;
    const user = {
      ...insertUser,
      id,
      password: null,
      isAdmin: false,
      isPrimaryAdmin: false,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.users.set(id, user);
    await this.logUserData(user);
    return user;
  }
  async logUserData(user) {
    try {
      const fs3 = await import("fs");
      const path4 = await import("path");
      const userData = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        name: user.name,
        whatsappNumber: user.whatsappNumber,
        id: user.id
      };
      const logEntry = JSON.stringify(userData) + "\n";
      const logPath = path4.join(process.cwd(), "user_data_log.txt");
      fs3.appendFileSync(logPath, logEntry);
    } catch (error) {
      console.error("Error logging user data:", error);
    }
  }
  async getProducts() {
    return Array.from(this.products.values());
  }
  async getProduct(id) {
    return this.products.get(id);
  }
  async getProductByCode(code) {
    return Array.from(this.products.values()).find(
      (product) => product.code === code
    );
  }
  async createProduct(insertProduct) {
    const id = this.currentProductId++;
    const product = {
      ...insertProduct,
      id,
      material: insertProduct.material || "",
      imageUrl: insertProduct.imageUrl || null,
      imageUrls: insertProduct.imageUrls || null,
      description: insertProduct.description || null,
      status: insertProduct.status || null,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.products.set(id, product);
    return product;
  }
  async updateProduct(id, updateData) {
    const existing = this.products.get(id);
    if (!existing) return void 0;
    const updated = {
      ...existing,
      ...updateData,
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.products.set(id, updated);
    return updated;
  }
  async deleteProduct(id) {
    return this.products.delete(id);
  }
  async searchProducts(query, limit, offset) {
    const lowercaseQuery = query.toLowerCase();
    let results = Array.from(this.products.values()).filter(
      (product) => product.name.toLowerCase().includes(lowercaseQuery) || product.code.toLowerCase().includes(lowercaseQuery) || product.category.toLowerCase().includes(lowercaseQuery) || product.finish.toLowerCase().includes(lowercaseQuery)
    );
    if (offset !== void 0) {
      results = results.slice(offset);
    }
    if (limit !== void 0) {
      results = results.slice(0, limit);
    }
    return results;
  }
  async getSearchCount(query) {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this.products.values()).filter(
      (product) => product.name.toLowerCase().includes(lowercaseQuery) || product.code.toLowerCase().includes(lowercaseQuery) || product.category.toLowerCase().includes(lowercaseQuery) || product.finish.toLowerCase().includes(lowercaseQuery)
    ).length;
  }
  async filterProducts(filters) {
    let products2 = Array.from(this.products.values());
    if (filters.category) {
      products2 = products2.filter((p) => p.category === filters.category);
    }
    if (filters.finish) {
      products2 = products2.filter((p) => p.finish === filters.finish);
    }
    if (filters.material) {
      products2 = products2.filter((p) => p.material === filters.material);
    }
    switch (filters.sortBy) {
      case "code":
        products2.sort((a, b) => a.code.localeCompare(b.code));
        break;
      case "category":
        products2.sort((a, b) => a.category.localeCompare(b.category));
        break;
      case "newest":
        products2.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      default:
        products2.sort((a, b) => a.name.localeCompare(b.name));
    }
    if (filters.offset !== void 0) {
      products2 = products2.slice(filters.offset);
    }
    if (filters.limit !== void 0) {
      products2 = products2.slice(0, filters.limit);
    }
    return products2;
  }
  async getFilterCount(filters) {
    let products2 = Array.from(this.products.values());
    if (filters.category) {
      products2 = products2.filter((p) => p.category === filters.category);
    }
    if (filters.finish) {
      products2 = products2.filter((p) => p.finish === filters.finish);
    }
    if (filters.material) {
      products2 = products2.filter((p) => p.material === filters.material);
    }
    return products2.length;
  }
  async bulkCreateProducts(insertProducts) {
    const products2 = [];
    for (const insertProduct of insertProducts) {
      const id = this.currentProductId++;
      const product = {
        ...insertProduct,
        id,
        material: insertProduct.material || "",
        imageUrl: insertProduct.imageUrl || null,
        imageUrls: insertProduct.imageUrls || null,
        description: insertProduct.description || null,
        status: insertProduct.status || null,
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      this.products.set(id, product);
      products2.push(product);
    }
    return products2;
  }
  async getWishlistByUser(userId) {
    const userWishlists = Array.from(this.wishlists.values()).filter(
      (wishlist) => wishlist.userId === userId
    );
    return userWishlists.map((wishlist) => {
      const product = this.products.get(wishlist.productId);
      if (!product) throw new Error(`Product not found for wishlist item`);
      return { ...wishlist, product };
    });
  }
  async addToWishlist(insertWishlist) {
    const id = this.currentWishlistId++;
    const wishlist = {
      ...insertWishlist,
      id,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.wishlists.set(id, wishlist);
    return wishlist;
  }
  async removeFromWishlist(userId, productId) {
    const wishlistItem = Array.from(this.wishlists.values()).find(
      (w) => w.userId === userId && w.productId === productId
    );
    if (!wishlistItem) return false;
    return this.wishlists.delete(wishlistItem.id);
  }
  async isInWishlist(userId, productId) {
    return Array.from(this.wishlists.values()).some(
      (w) => w.userId === userId && w.productId === productId
    );
  }
  // New methods for categories and finishes
  async getCategories() {
    const allProducts = Array.from(this.products.values());
    const categories = /* @__PURE__ */ new Set();
    allProducts.forEach((product) => {
      if (product.category) {
        categories.add(product.category);
      }
    });
    return Array.from(categories);
  }
  async getFinishes() {
    const allProducts = Array.from(this.products.values());
    const finishes = /* @__PURE__ */ new Set();
    allProducts.forEach((product) => {
      if (product.finish) {
        finishes.add(product.finish);
      }
    });
    return Array.from(finishes);
  }
  async getMaterials() {
    const allProducts = Array.from(this.products.values());
    const materials = /* @__PURE__ */ new Set();
    allProducts.forEach((product) => {
      if (product.material) {
        materials.add(product.material);
      }
    });
    return Array.from(materials);
  }
  // Dynamic filter methods that return only available options based on current selections
  async getAvailableCategories(filters) {
    let products2 = Array.from(this.products.values());
    if (filters.finish) {
      products2 = products2.filter((p) => p.finish === filters.finish);
    }
    if (filters.material) {
      products2 = products2.filter((p) => p.material === filters.material);
    }
    const categories = /* @__PURE__ */ new Set();
    products2.forEach((product) => {
      if (product.category) {
        categories.add(product.category);
      }
    });
    return Array.from(categories).sort();
  }
  async getAvailableFinishes(filters) {
    let products2 = Array.from(this.products.values());
    if (filters.category) {
      products2 = products2.filter((p) => p.category === filters.category);
    }
    if (filters.material) {
      products2 = products2.filter((p) => p.material === filters.material);
    }
    const finishes = /* @__PURE__ */ new Set();
    products2.forEach((product) => {
      if (product.finish) {
        finishes.add(product.finish);
      }
    });
    return Array.from(finishes).sort();
  }
  async getAvailableMaterials(filters) {
    let products2 = Array.from(this.products.values());
    if (filters.category) {
      products2 = products2.filter((p) => p.category === filters.category);
    }
    if (filters.finish) {
      products2 = products2.filter((p) => p.finish === filters.finish);
    }
    const materials = /* @__PURE__ */ new Set();
    products2.forEach((product) => {
      if (product.material) {
        materials.add(product.material);
      }
    });
    return Array.from(materials).sort();
  }
  // Add new method to get all users
  async getAllUsers() {
    return Array.from(this.users.values());
  }
  async updateUserPassword(id, password) {
    const user = this.users.get(id);
    if (!user) return false;
    user.password = password;
    this.users.set(id, user);
    return true;
  }
  async updateUser(id, data) {
    const user = this.users.get(id);
    if (!user) return void 0;
    if (data.isPrimaryAdmin !== void 0 && !user.isPrimaryAdmin) {
      data.isPrimaryAdmin = false;
    }
    const updated = { ...user, ...data };
    this.users.set(id, updated);
    return updated;
  }
  async deleteUser(id) {
    const user = this.users.get(id);
    if (!user || user.isPrimaryAdmin) return false;
    return this.users.delete(id);
  }
};
var DatabaseStorage = class {
  async getUser(id) {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || void 0;
  }
  async getUserByWhatsApp(whatsappNumber) {
    const [user] = await db.select().from(users).where(eq(users.whatsappNumber, whatsappNumber));
    return user || void 0;
  }
  async createUser(insertUser) {
    const [user] = await db.insert(users).values(insertUser).returning();
    await this.logUserData(user);
    return user;
  }
  async logUserData(user) {
    try {
      const fs3 = await import("fs");
      const path4 = await import("path");
      const userData = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        name: user.name,
        whatsappNumber: user.whatsappNumber,
        id: user.id
      };
      const logEntry = JSON.stringify(userData) + "\n";
      const logPath = path4.join(process.cwd(), "user_data_log.txt");
      fs3.appendFileSync(logPath, logEntry);
    } catch (error) {
      console.error("Error logging user data:", error);
    }
  }
  async getProducts() {
    return await db.select().from(products);
  }
  async getProduct(id) {
    const [product] = await db.select().from(products).where(eq(products.id, id));
    return product || void 0;
  }
  async getProductByCode(code) {
    const [product] = await db.select().from(products).where(eq(products.code, code));
    return product || void 0;
  }
  async createProduct(insertProduct) {
    const [product] = await db.insert(products).values({
      ...insertProduct,
      imageUrl: insertProduct.imageUrl || null
    }).returning();
    return product;
  }
  async updateProduct(id, updateData) {
    const [product] = await db.update(products).set({
      ...updateData,
      imageUrl: updateData.imageUrl || null,
      updatedAt: /* @__PURE__ */ new Date()
    }).where(eq(products.id, id)).returning();
    return product || void 0;
  }
  async deleteProduct(id) {
    const result = await db.delete(products).where(eq(products.id, id));
    return result.rowCount > 0;
  }
  async searchProducts(query, limit, offset) {
    const lowercaseQuery = `%${query.toLowerCase()}%`;
    let searchQuery = db.select().from(products).where(
      or(
        sql`LOWER(${products.name}) LIKE ${lowercaseQuery}`,
        sql`LOWER(${products.code}) LIKE ${lowercaseQuery}`,
        sql`LOWER(${products.category}) LIKE ${lowercaseQuery}`,
        sql`LOWER(${products.finish}) LIKE ${lowercaseQuery}`
      )
    );
    if (limit !== void 0) {
      searchQuery = searchQuery.limit(limit);
    }
    if (offset !== void 0) {
      searchQuery = searchQuery.offset(offset);
    }
    return await searchQuery;
  }
  async getSearchCount(query) {
    const lowercaseQuery = `%${query.toLowerCase()}%`;
    const result = await db.select({ count: sql`count(*)` }).from(products).where(
      or(
        sql`LOWER(${products.name}) LIKE ${lowercaseQuery}`,
        sql`LOWER(${products.code}) LIKE ${lowercaseQuery}`,
        sql`LOWER(${products.category}) LIKE ${lowercaseQuery}`,
        sql`LOWER(${products.finish}) LIKE ${lowercaseQuery}`
      )
    );
    return result[0]?.count || 0;
  }
  async filterProducts(filters) {
    let query = db.select().from(products);
    const conditions = [];
    if (filters.category) {
      conditions.push(eq(products.category, filters.category));
    }
    if (filters.finish) {
      conditions.push(eq(products.finish, filters.finish));
    }
    if (filters.material) {
      conditions.push(eq(products.material, filters.material));
    }
    if (conditions.length > 0) {
      query = query.where(conditions.length === 1 ? conditions[0] : and(...conditions));
    }
    switch (filters.sortBy) {
      case "code":
        query = query.orderBy(asc(products.code));
        break;
      case "category":
        query = query.orderBy(asc(products.category));
        break;
      case "newest":
        query = query.orderBy(desc(products.createdAt));
        break;
      default:
        query = query.orderBy(asc(products.name));
    }
    if (filters.limit !== void 0) {
      query = query.limit(filters.limit);
    }
    if (filters.offset !== void 0) {
      query = query.offset(filters.offset);
    }
    return await query;
  }
  async getFilterCount(filters) {
    let query = db.select({ count: sql`count(*)` }).from(products);
    const conditions = [];
    if (filters.category) {
      conditions.push(eq(products.category, filters.category));
    }
    if (filters.finish) {
      conditions.push(eq(products.finish, filters.finish));
    }
    if (filters.material) {
      conditions.push(eq(products.material, filters.material));
    }
    if (conditions.length > 0) {
      query = query.where(conditions.length === 1 ? conditions[0] : and(...conditions));
    }
    const result = await query;
    return result[0]?.count || 0;
  }
  async bulkCreateProducts(insertProducts) {
    const formattedProducts = insertProducts.map((p) => ({
      ...p,
      imageUrl: p.imageUrl || null
    }));
    const createdProducts = await db.insert(products).values(formattedProducts).returning();
    return createdProducts;
  }
  async getWishlistByUser(userId) {
    const wishlistWithProducts = await db.select({
      id: wishlists.id,
      userId: wishlists.userId,
      productId: wishlists.productId,
      createdAt: wishlists.createdAt,
      product: products
    }).from(wishlists).innerJoin(products, eq(wishlists.productId, products.id)).where(eq(wishlists.userId, userId));
    return wishlistWithProducts;
  }
  async addToWishlist(insertWishlist) {
    const [wishlist] = await db.insert(wishlists).values(insertWishlist).returning();
    return wishlist;
  }
  async removeFromWishlist(userId, productId) {
    const result = await db.delete(wishlists).where(eq(wishlists.userId, userId) && eq(wishlists.productId, productId));
    return result.rowCount > 0;
  }
  async isInWishlist(userId, productId) {
    const [wishlistItem] = await db.select().from(wishlists).where(eq(wishlists.userId, userId) && eq(wishlists.productId, productId));
    return !!wishlistItem;
  }
  // New methods for categories and finishes
  async getCategories() {
    const result = await db.selectDistinct({ category: products.category }).from(products);
    return result.map((r) => r.category).filter(Boolean);
  }
  async getFinishes() {
    const result = await db.selectDistinct({ finish: products.finish }).from(products);
    return result.map((r) => r.finish).filter(Boolean);
  }
  async getMaterials() {
    const result = await db.selectDistinct({ material: products.material }).from(products);
    return result.map((r) => r.material).filter(Boolean);
  }
  // Add new method to get all users
  async getAllUsers() {
    return await db.select().from(users);
  }
  async updateUserPassword(id, password) {
    const result = await db.update(users).set({ password }).where(eq(users.id, id));
    return result.rowCount > 0;
  }
  async updateUser(id, data) {
    if (data.isPrimaryAdmin !== void 0 || data.isAdmin !== void 0) {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      if (!user) return void 0;
      if (data.isPrimaryAdmin !== void 0 && !user.isPrimaryAdmin) {
        data.isPrimaryAdmin = false;
      }
    }
    const [updated] = await db.update(users).set(data).where(eq(users.id, id)).returning();
    return updated;
  }
  async deleteUser(id) {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    if (!user || user.isPrimaryAdmin) return false;
    const result = await db.delete(users).where(eq(users.id, id));
    return result.rowCount > 0;
  }
  // Dynamic filter methods that return only available options based on current selections
  async getAvailableCategories(filters) {
    let query = db.selectDistinct({ category: products.category }).from(products);
    const conditions = [];
    if (filters.finish) {
      conditions.push(eq(products.finish, filters.finish));
    }
    if (filters.material) {
      conditions.push(eq(products.material, filters.material));
    }
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    const result = await query;
    return result.map((r) => r.category).filter(Boolean).sort();
  }
  async getAvailableFinishes(filters) {
    let query = db.selectDistinct({ finish: products.finish }).from(products);
    const conditions = [];
    if (filters.category) {
      conditions.push(eq(products.category, filters.category));
    }
    if (filters.material) {
      conditions.push(eq(products.material, filters.material));
    }
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    const result = await query;
    return result.map((r) => r.finish).filter(Boolean).sort();
  }
  async getAvailableMaterials(filters) {
    let query = db.selectDistinct({ material: products.material }).from(products);
    const conditions = [];
    if (filters.category) {
      conditions.push(eq(products.category, filters.category));
    }
    if (filters.finish) {
      conditions.push(eq(products.finish, filters.finish));
    }
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    const result = await query;
    return result.map((r) => r.material).filter(Boolean).sort();
  }
};
var isDev2 = process.env.NODE_ENV === "development";
var storage = isDev2 ? new MemStorage() : new DatabaseStorage();

// server/routes.ts
import { eq as eq2 } from "drizzle-orm";
import multer from "multer";
import * as XLSX from "xlsx";
import * as fs from "fs";
import * as path from "path";
import bcrypt from "bcrypt";
import csvParser from "csv-parser";
var upload = multer({
  dest: "uploads/",
  limits: {
    fileSize: 100 * 1024 * 1024
    // 100MB in bytes
  }
});
var uploadsDir = path.join(process.cwd(), "uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}
async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}
async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}
async function registerRoutes(app2) {
  app2.post("/api/init-db", async (req, res) => {
    try {
      console.log("Database initialization requested");
      const { initKey } = req.body;
      const validKeys = [process.env.SESSION_SECRET, "default-init-key"];
      if (!validKeys.includes(initKey)) {
        return res.status(401).json({ error: "Unauthorized" });
      }
      try {
        await db.select().from(users).limit(1);
        console.log("Database tables already exist");
      } catch (error) {
        if (error.code === "42P01") {
          console.log("Creating database tables...");
          await pool.query(`CREATE TABLE IF NOT EXISTS "users" ("id" serial PRIMARY KEY NOT NULL, "name" varchar(255) NOT NULL, "whatsappNumber" varchar(20) NOT NULL, "password" varchar(255), "isAdmin" boolean DEFAULT false NOT NULL, "isPrimaryAdmin" boolean DEFAULT false NOT NULL, "createdAt" timestamp DEFAULT now() NOT NULL, "updatedAt" timestamp DEFAULT now() NOT NULL, CONSTRAINT "users_whatsappNumber_unique" UNIQUE("whatsappNumber"));`);
          await pool.query(`CREATE TABLE IF NOT EXISTS "products" ("id" serial PRIMARY KEY NOT NULL, "name" varchar(255) NOT NULL, "code" varchar(100) NOT NULL, "category" varchar(100), "length" numeric(10,2) DEFAULT 0, "breadth" numeric(10,2) DEFAULT 0, "height" numeric(10,2) DEFAULT 0, "finish" varchar(100), "material" varchar(100), "imageUrl" varchar(500), "imageUrls" text, "description" text, "status" varchar(50), "createdAt" timestamp DEFAULT now() NOT NULL, "updatedAt" timestamp DEFAULT now() NOT NULL, CONSTRAINT "products_code_unique" UNIQUE("code"));`);
          await pool.query(`CREATE TABLE IF NOT EXISTS "wishlists" ("id" serial PRIMARY KEY NOT NULL, "userId" integer NOT NULL, "productId" integer NOT NULL, "createdAt" timestamp DEFAULT now() NOT NULL, CONSTRAINT "wishlists_userId_users_id_fk" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action, "wishlists_productId_products_id_fk" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE cascade ON UPDATE no action);`);
          await pool.query(`CREATE TABLE IF NOT EXISTS "user_sessions" ("sid" varchar NOT NULL, "sess" json NOT NULL, "expire" timestamp(6) NOT NULL, CONSTRAINT "session_pkey" PRIMARY KEY ("sid"));`);
          console.log("Database tables created successfully");
        } else {
          throw error;
        }
      }
      const existingAdmin = await db.select().from(users).where(eq2(users.isPrimaryAdmin, true)).limit(1);
      if (existingAdmin.length > 0) {
        return res.json({ message: "Database already initialized", admin: existingAdmin[0].name });
      }
      const newAdmin = await db.insert(users).values({
        name: "Admin User",
        whatsappNumber: "+918882636296",
        password: null,
        isAdmin: true,
        isPrimaryAdmin: true
      }).returning();
      res.json({ message: "Database initialized successfully", admin: newAdmin[0].name, whatsappNumber: newAdmin[0].whatsappNumber });
    } catch (error) {
      console.error("Database initialization error:", error);
      res.status(500).json({ error: "Failed to initialize database" });
    }
  });
  app2.post("/api/auth/register", async (req, res) => {
    try {
      const { name, whatsappNumber } = insertUserSchema.parse(req.body);
      let user;
      try {
        user = await storage.getUserByWhatsApp(whatsappNumber);
        if (!user) {
          user = await storage.createUser({ name, whatsappNumber });
        }
      } catch (storageError) {
        console.error("Storage error:", storageError);
        return res.status(500).json({
          message: "Registration failed. Please try again."
        });
      }
      if (req.session) {
        req.session.userId = user.id;
      }
      res.json({ user, success: true });
    } catch (error) {
      console.error("Registration error:", error);
      res.status(400).json({
        message: error.message.includes("require") ? "Registration failed. Please try again." : error.message
      });
    }
  });
  app2.get("/api/categories", async (_req, res) => {
    try {
      const categories = await storage.getCategories();
      res.json(categories);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/finishes", async (_req, res) => {
    try {
      const finishes = await storage.getFinishes();
      res.json(finishes);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/materials", async (_req, res) => {
    try {
      const materials = await storage.getMaterials();
      res.json(materials);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/filters/categories", async (req, res) => {
    try {
      const { finish, material } = req.query;
      const categories = await storage.getAvailableCategories({
        finish: finish === "all" ? void 0 : finish,
        material: material === "all" ? void 0 : material
      });
      res.json(categories);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/filters/finishes", async (req, res) => {
    try {
      const { category, material } = req.query;
      const finishes = await storage.getAvailableFinishes({
        category: category === "all" ? void 0 : category,
        material: material === "all" ? void 0 : material
      });
      res.json(finishes);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/filters/materials", async (req, res) => {
    try {
      const { category, finish } = req.query;
      const materials = await storage.getAvailableMaterials({
        category: category === "all" ? void 0 : category,
        finish: finish === "all" ? void 0 : finish
      });
      res.json(materials);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/wishlist", async (req, res) => {
    try {
      const userId = req.session?.userId;
      if (!userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const wishlist = await storage.getWishlistByUser(userId);
      res.json(wishlist);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/wishlist", async (req, res) => {
    try {
      const userId = req.session?.userId;
      if (!userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const { productId } = insertWishlistSchema.parse({ ...req.body, userId });
      const isInWishlist = await storage.isInWishlist(userId, productId);
      if (isInWishlist) {
        return res.status(400).json({ message: "Product already in wishlist" });
      }
      const wishlist = await storage.addToWishlist({ userId, productId });
      res.json(wishlist);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  app2.delete("/api/wishlist/:productId", async (req, res) => {
    try {
      const userId = req.session?.userId;
      if (!userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const productId = parseInt(req.params.productId);
      const success = await storage.removeFromWishlist(userId, productId);
      if (!success) {
        return res.status(404).json({ message: "Item not found in wishlist" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/auth/admin-login", async (req, res) => {
    try {
      console.log("Admin login attempt:", {
        whatsappNumber: req.body.whatsappNumber,
        hasPassword: !!req.body.password
      });
      const { whatsappNumber, password } = adminLoginSchema.parse(req.body);
      const user = await storage.getUserByWhatsApp(whatsappNumber);
      console.log("User found:", user ? {
        id: user.id,
        isAdmin: user.isAdmin,
        hasPassword: !!user.password
      } : "No user found");
      if (!user || !user.isAdmin) {
        return res.status(401).json({ message: "Invalid admin credentials" });
      }
      if (!user.password) {
        console.log("Setting password for admin without password");
        const hashedPassword = await hashPassword(password);
        await storage.updateUserPassword(user.id, hashedPassword);
      } else {
        const isPasswordValid = await verifyPassword(password, user.password);
        console.log("Password verification result:", isPasswordValid);
        if (!isPasswordValid) {
          return res.status(401).json({ message: "Invalid admin credentials" });
        }
      }
      if (req.session) {
        req.session.userId = user.id;
        console.log("Session set with userId:", user.id);
      } else {
        console.error("No session object available");
      }
      const { password: _, ...userWithoutPassword } = user;
      res.json({ user: userWithoutPassword, success: true });
    } catch (error) {
      console.error("Admin login error:", error);
      res.status(400).json({ message: error.message });
    }
  });
  app2.post("/api/auth/logout", async (req, res) => {
    if (req.session) {
      req.session.destroy((err) => {
        if (err) {
          res.status(500).json({ message: "Could not log out" });
        } else {
          res.json({ success: true });
        }
      });
    } else {
      res.json({ success: true });
    }
  });
  app2.get("/api/auth/me", async (req, res) => {
    const userId = req.session?.userId;
    if (!userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }
    res.json({ user });
  });
  app2.get("/api/products", async (req, res) => {
    try {
      const { search, category, finish, material, sortBy, page = "1", limit = "50" } = req.query;
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;
      let products2;
      let total = 0;
      if (search) {
        products2 = await storage.searchProducts(search, limitNum, offset);
        total = await storage.getSearchCount(search);
      } else {
        products2 = await storage.filterProducts({
          category: category === "all" ? void 0 : category,
          finish: finish === "all" ? void 0 : finish,
          material: material === "all" ? void 0 : material,
          sortBy,
          limit: limitNum,
          offset
        });
        total = await storage.getFilterCount({
          category: category === "all" ? void 0 : category,
          finish: finish === "all" ? void 0 : finish,
          material: material === "all" ? void 0 : material
        });
      }
      res.json({
        products: products2,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/products/:id", async (req, res) => {
    try {
      const product = await storage.getProduct(parseInt(req.params.id));
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }
      res.json(product);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/products", async (req, res) => {
    try {
      const userId = req.session?.userId;
      if (!userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const user = await storage.getUser(userId);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const productData = insertProductSchema.parse(req.body);
      const product = await storage.createProduct(productData);
      res.json(product);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  app2.put("/api/products/:id", async (req, res) => {
    try {
      const userId = req.session?.userId;
      if (!userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const user = await storage.getUser(userId);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const productData = insertProductSchema.partial().parse(req.body);
      const product = await storage.updateProduct(parseInt(req.params.id), productData);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }
      res.json(product);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  app2.delete("/api/products/:id", async (req, res) => {
    try {
      const userId = req.session?.userId;
      if (!userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const user = await storage.getUser(userId);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const success = await storage.deleteProduct(parseInt(req.params.id));
      if (!success) {
        return res.status(404).json({ message: "Product not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/products/upload-excel", upload.single("excel"), async (req, res) => {
    try {
      const userId = req.session?.userId;
      if (!userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const user = await storage.getUser(userId);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }
      const fileExtension = path.extname(req.file.originalname).toLowerCase();
      let products2 = [];
      console.log(`Processing file: ${req.file.originalname}, size: ${req.file.size}, type: ${fileExtension}`);
      if (fileExtension === ".xlsx" || fileExtension === ".xls") {
        const workbook = XLSX.readFile(req.file.path);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);
        console.log(`Excel data rows: ${data.length}, Sample row:`, data.length > 0 ? data[0] : "No data");
        products2 = data.map((row) => ({
          name: row.name || row.Name || "",
          code: row.code || row.Code || "",
          category: row.category || row.Category || "",
          length: parseFloat(row.length || row.Length || "0"),
          breadth: parseFloat(row.breadth || row.Breadth || "0"),
          height: parseFloat(row.height || row.Height || "0"),
          finish: row.finish || row.Finish || "",
          material: row.material || row.Material || "",
          imageUrl: row.imageUrl || row.ImageUrl || row.image_url || ""
        }));
      } else if (fileExtension === ".csv") {
        const data = [];
        await new Promise((resolve, reject) => {
          fs.createReadStream(req.file.path).pipe(csvParser()).on("data", (row) => {
            if (data.length < 2) {
              console.log("CSV row sample:", row);
            }
            data.push(row);
          }).on("end", resolve).on("error", reject);
        });
        console.log(`CSV data rows: ${data.length}`);
        const cleanedData = data.map((row) => {
          const cleanedRow = {};
          Object.keys(row).forEach((key) => {
            const cleanKey = key.replace(/^\uFEFF/, "").trim().toLowerCase();
            cleanedRow[cleanKey] = typeof row[key] === "string" ? row[key].trim() : row[key];
          });
          return cleanedRow;
        });
        console.log("Cleaned CSV row sample:", cleanedData[0]);
        const nameKeys = ["name", "product_name", "productname", "product name"];
        const codeKeys = ["code", "product_code", "productcode", "product code", "sku"];
        products2 = cleanedData.map((row) => {
          let name = "";
          let code = "";
          for (const key of nameKeys) {
            if (row[key] !== void 0 && row[key] !== null && row[key] !== "") {
              name = row[key];
              break;
            }
          }
          for (const key of codeKeys) {
            if (row[key] !== void 0 && row[key] !== null && row[key] !== "") {
              code = row[key];
              break;
            }
          }
          if (!name || !code) {
            for (const key in row) {
              const value = row[key];
              if (!value) continue;
              if (!name && key.toLowerCase().includes("name")) {
                name = value;
              }
              if (!code && (key.toLowerCase().includes("code") || key.toLowerCase().includes("sku"))) {
                code = value;
              }
            }
          }
          return {
            name,
            code,
            category: row.category || "",
            length: parseFloat(row.length || "0"),
            breadth: parseFloat(row.breadth || "0"),
            height: parseFloat(row.height || "0"),
            finish: row.finish || "",
            material: row.material || "",
            imageUrl: row.imageurl || row.image_url || ""
          };
        });
      } else {
        return res.status(400).json({ message: "Unsupported file format. Please upload XLSX, XLS, or CSV file." });
      }
      const validProducts = products2.filter((p) => p.name && p.code);
      console.log(`Total products: ${products2.length}, Valid products: ${validProducts.length}`);
      if (products2.length > 0 && validProducts.length === 0) {
        console.log("Sample invalid product:", products2[0]);
      }
      const createdProducts = await storage.bulkCreateProducts(validProducts);
      res.json({
        success: true,
        imported: createdProducts.length,
        total: products2.length
      });
    } catch (error) {
      console.error("Excel upload error:", error);
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/wishlist/export/excel", async (req, res) => {
    try {
      const requestedUserId = req.query.userId ? parseInt(req.query.userId) : null;
      const adminId = req.session?.userId;
      let userId;
      if (requestedUserId && adminId) {
        const admin = await storage.getUser(adminId);
        if (!admin?.isAdmin) {
          return res.status(403).json({ message: "Admin access required to export other users' wishlists" });
        }
        userId = requestedUserId;
      } else {
        userId = req.session?.userId;
        if (!userId) {
          return res.status(401).json({ message: "Not authenticated" });
        }
      }
      const wishlistItems = await storage.getWishlistByUser(userId);
      const user = await storage.getUser(userId);
      if (!wishlistItems.length) {
        return res.status(404).json({ message: "No items in wishlist" });
      }
      const workbook = XLSX.utils.book_new();
      const data = wishlistItems.map((item, index) => ({
        "No.": index + 1,
        "Code": item.product.code,
        "Name": item.product.name,
        "Category": item.product.category,
        "Material": item.product.material || "Not specified",
        "Dimensions (cm)": `${item.product.length}\xD7${item.product.breadth}\xD7${item.product.height}`,
        "Finish": item.product.finish
      }));
      const header = [
        ["Vmake Finessee - Customer Wishlist"],
        [`Customer: ${user?.name}`],
        [`Generated on: ${(/* @__PURE__ */ new Date()).toLocaleDateString()}`],
        [""],
        // Empty row for spacing
        []
        // Empty row before the actual headers
      ];
      const ws = XLSX.utils.aoa_to_sheet(header);
      if (!ws["!merges"]) ws["!merges"] = [];
      ws["!merges"].push({ s: { r: 0, c: 0 }, e: { r: 0, c: 5 } });
      ws["!merges"].push({ s: { r: 1, c: 0 }, e: { r: 1, c: 5 } });
      ws["!merges"].push({ s: { r: 2, c: 0 }, e: { r: 2, c: 5 } });
      XLSX.utils.sheet_add_json(ws, data, { origin: 5 });
      XLSX.utils.book_append_sheet(workbook, ws, "Wishlist");
      const filename = `Vmake_Finessee_Wishlist_${user?.name.replace(/\s+/g, "_")}_${Date.now()}.xlsx`;
      const filePath = path.join("uploads", filename);
      XLSX.writeFile(workbook, filePath);
      res.download(filePath, filename, (err) => {
        if (err) {
          console.error("Error sending file:", err);
        }
        fs.unlink(filePath, (unlinkErr) => {
          if (unlinkErr) {
            console.error("Error deleting file:", unlinkErr);
          }
        });
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/users", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const users2 = await storage.getAllUsers();
      const sanitizedUsers = users2.map((user) => {
        const { password, ...rest } = user;
        return rest;
      });
      res.json(sanitizedUsers);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/admin/users/:userId", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const userId = parseInt(req.params.userId);
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      const wishlist = await storage.getWishlistByUser(userId);
      const { password, ...sanitizedUser } = user;
      res.json({
        user: sanitizedUser,
        wishlist
      });
    } catch (error) {
      console.error("Error fetching user details:", error);
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/users", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const { name, whatsappNumber, password, isAdmin } = req.body;
      const existingUser = await storage.getUserByWhatsApp(whatsappNumber);
      if (existingUser) {
        return res.status(400).json({ message: "User with this WhatsApp number already exists" });
      }
      const user = await storage.createUser({ name, whatsappNumber });
      if (password || isAdmin) {
        const updates = {};
        if (password) {
          updates.password = await hashPassword(password);
        }
        if (isAdmin) {
          updates.isAdmin = true;
          if (admin.isPrimaryAdmin) {
            updates.isPrimaryAdmin = false;
          } else {
            updates.isAdmin = false;
          }
        }
        await storage.updateUser(user.id, updates);
      }
      const updatedUser = await storage.getUser(user.id);
      const { password: _, ...sanitizedUser } = updatedUser;
      res.json(sanitizedUser);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  app2.put("/api/users/:id", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const userId = parseInt(req.params.id);
      const { name, whatsappNumber, password, isAdmin, isPrimaryAdmin } = req.body;
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      if (user.isPrimaryAdmin && !admin.isPrimaryAdmin) {
        return res.status(403).json({ message: "Only primary admin can modify another primary admin" });
      }
      const updates = {};
      if (name) updates.name = name;
      if (whatsappNumber) updates.whatsappNumber = whatsappNumber;
      if (password) updates.password = await hashPassword(password);
      if (admin.isPrimaryAdmin) {
        if (isAdmin !== void 0) updates.isAdmin = isAdmin;
        if (isPrimaryAdmin !== void 0) updates.isPrimaryAdmin = isPrimaryAdmin;
      }
      const updatedUser = await storage.updateUser(userId, updates);
      const { password: _, ...sanitizedUser } = updatedUser;
      res.json(sanitizedUser);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  app2.delete("/api/users/:id", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const userId = parseInt(req.params.id);
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      if (user.isPrimaryAdmin) {
        return res.status(403).json({ message: "Cannot delete primary admin" });
      }
      if (userId === adminId) {
        return res.status(400).json({ message: "Cannot delete your own account" });
      }
      const success = await storage.deleteUser(userId);
      if (!success) {
        return res.status(500).json({ message: "Failed to delete user" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/users/export", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const users2 = await storage.getAllUsers();
      const workbook = XLSX.utils.book_new();
      const data = users2.map((user, index) => ({
        "No.": index + 1,
        "Name": user.name,
        "WhatsApp Number": user.whatsappNumber,
        "Admin": user.isAdmin ? "Yes" : "No",
        "Primary Admin": user.isPrimaryAdmin ? "Yes" : "No",
        "Registered On": new Date(user.createdAt).toLocaleDateString()
      }));
      const ws = XLSX.utils.json_to_sheet(data);
      XLSX.utils.book_append_sheet(workbook, ws, "Users");
      const filename = `Vmake_Finessee_Users_${Date.now()}.xlsx`;
      const filePath = path.join("uploads", filename);
      XLSX.writeFile(workbook, filePath);
      res.download(filePath, filename, (err) => {
        if (err) {
          console.error("Error sending file:", err);
        }
        fs.unlink(filePath, (unlinkErr) => {
          if (unlinkErr) {
            console.error("Error deleting file:", unlinkErr);
          }
        });
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  let mockFeedback = [
    {
      id: 1,
      userId: 1,
      productId: 1,
      customerName: "Priya Sharma",
      customerPhone: "+919876543210",
      rating: 5,
      title: "Absolutely Beautiful Brass Ganesha!",
      message: "The craftsmanship is exceptional. The intricate details on this brass Ganesha idol are stunning. It's become the centerpiece of our home temple. Highly recommend Vmake Finessee for authentic handcrafted pieces.",
      isApproved: true,
      isPublished: true,
      adminNotes: "Excellent feedback, customer very satisfied",
      createdAt: /* @__PURE__ */ new Date("2024-05-15"),
      updatedAt: /* @__PURE__ */ new Date("2024-05-15")
    },
    {
      id: 2,
      userId: 2,
      productId: 2,
      customerName: "Rajesh Kumar",
      customerPhone: "+919123456789",
      rating: 4,
      title: "Great Quality Home Decor",
      message: "Ordered a decorative brass bowl for our living room. The quality is excellent and the finish is perfect. Delivery was prompt. Will definitely order more items.",
      isApproved: false,
      isPublished: false,
      adminNotes: null,
      createdAt: /* @__PURE__ */ new Date("2024-05-20"),
      updatedAt: /* @__PURE__ */ new Date("2024-05-20")
    },
    {
      id: 3,
      userId: 3,
      productId: 1,
      customerName: "Meera Patel",
      customerPhone: "+919988776655",
      rating: 5,
      title: "Perfect for Diwali Decoration",
      message: "Bought this for Diwali and it was perfect! The brass work is authentic and the size is just right. My guests complimented the beautiful piece. Thank you Vmake Finessee!",
      isApproved: true,
      isPublished: true,
      adminNotes: "Good feedback, consider publishing",
      createdAt: /* @__PURE__ */ new Date("2024-05-25"),
      updatedAt: /* @__PURE__ */ new Date("2024-05-25")
    },
    {
      id: 4,
      userId: 4,
      productId: 3,
      customerName: "Anita Singh",
      customerPhone: "+919876543211",
      rating: 4,
      title: "Beautiful Craftsmanship",
      message: "The attention to detail in this brass piece is remarkable. It adds such elegance to our home. The packaging was also excellent. Highly recommended!",
      isApproved: true,
      isPublished: true,
      adminNotes: "Great customer satisfaction",
      createdAt: /* @__PURE__ */ new Date("2024-05-28"),
      updatedAt: /* @__PURE__ */ new Date("2024-05-28")
    },
    {
      id: 5,
      userId: 5,
      productId: 2,
      customerName: "Vikram Joshi",
      customerPhone: "+919123456788",
      rating: 5,
      title: "Exceeded Expectations",
      message: "The quality is outstanding! This brass artifact is even more beautiful in person. The finish is perfect and it arrived safely. Will definitely be a repeat customer.",
      isApproved: true,
      isPublished: true,
      adminNotes: "Excellent review",
      createdAt: /* @__PURE__ */ new Date("2024-05-30"),
      updatedAt: /* @__PURE__ */ new Date("2024-05-30")
    }
  ];
  app2.get("/api/feedback", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      res.json(mockFeedback);
    } catch (error) {
      console.error("Error fetching feedback:", error);
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/feedback/published", async (_req, res) => {
    try {
      const publishedFeedback = mockFeedback.filter((f) => f.isPublished);
      res.json(publishedFeedback);
    } catch (error) {
      console.error("Error fetching published feedback:", error);
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/feedback", async (req, res) => {
    try {
      const feedbackData = insertFeedbackSchema.parse(req.body);
      const newFeedback = {
        ...feedbackData,
        id: mockFeedback.length + 1,
        productId: feedbackData.productId || null,
        customerPhone: feedbackData.customerPhone || null,
        adminNotes: null,
        isApproved: false,
        isPublished: false,
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      mockFeedback.push(newFeedback);
      res.status(201).json(newFeedback);
    } catch (error) {
      console.error("Error creating feedback:", error);
      res.status(400).json({ message: error.message });
    }
  });
  app2.put("/api/feedback/:id", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const feedbackId = parseInt(req.params.id);
      const updates = req.body;
      const index = mockFeedback.findIndex((f) => f.id === feedbackId);
      if (index === -1) {
        return res.status(404).json({ message: "Feedback not found" });
      }
      mockFeedback[index] = {
        ...mockFeedback[index],
        ...updates,
        updatedAt: /* @__PURE__ */ new Date()
      };
      res.json(mockFeedback[index]);
    } catch (error) {
      console.error("Error updating feedback:", error);
      res.status(500).json({ message: error.message });
    }
  });
  app2.delete("/api/feedback/:id", async (req, res) => {
    try {
      const adminId = req.session?.userId;
      if (!adminId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const admin = await storage.getUser(adminId);
      if (!admin?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }
      const feedbackId = parseInt(req.params.id);
      const index = mockFeedback.findIndex((f) => f.id === feedbackId);
      if (index === -1) {
        return res.status(404).json({ message: "Feedback not found" });
      }
      mockFeedback.splice(index, 1);
      res.json({ message: "Feedback deleted successfully" });
    } catch (error) {
      console.error("Error deleting feedback:", error);
      res.status(500).json({ message: error.message });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs2 from "fs";
import path3 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path2 from "path";
var __dirname = path2.dirname(
  decodeURIComponent(new URL(import.meta.url).pathname)
);
var vite_config_default = defineConfig({
  plugins: [
    react(),
    // Only include Replit plugins in development and when REPL_ID is present
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      (await import("@replit/vite-plugin-runtime-error-modal")).default(),
      (await import("@replit/vite-plugin-cartographer")).cartographer()
    ] : []
  ],
  resolve: {
    alias: {
      "@": path2.resolve(__dirname, "client", "src"),
      "@shared": path2.resolve(__dirname, "shared"),
      "@assets": path2.resolve(__dirname, "attached_assets")
    }
  },
  root: path2.resolve(__dirname, "client"),
  build: {
    outDir: path2.resolve(__dirname, "dist/public"),
    emptyOutDir: true
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var __dirname2 = path3.dirname(
  decodeURIComponent(new URL(import.meta.url).pathname)
);
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path3.resolve(
        __dirname2,
        "..",
        "client",
        "index.html"
      );
      console.log("Looking for template at:", clientTemplate);
      let template = await fs2.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      console.error("Error loading template:", e);
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path3.resolve(__dirname2, "public");
  if (!fs2.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path3.resolve(distPath, "index.html"));
  });
}

// server/middleware.ts
function errorHandler(err, req, res, next) {
  console.error("Error:", err);
  let status = 500;
  let message = "Internal server error";
  let details = void 0;
  if (err.name === "ValidationError") {
    status = 400;
    message = "Validation error";
    details = err.message;
  } else if (err.name === "UnauthorizedError") {
    status = 401;
    message = "Unauthorized";
  } else if (err.name === "ForbiddenError") {
    status = 403;
    message = "Forbidden";
  } else if (err.name === "NotFoundError") {
    status = 404;
    message = "Not found";
  } else if (err.code === "LIMIT_FILE_SIZE") {
    status = 413;
    message = "File too large";
    details = "Maximum file size is 100MB";
  } else if (err.code === "ENOENT") {
    status = 404;
    message = "File not found";
  }
  if (process.env.NODE_ENV === "production" && status === 500) {
    message = "Something went wrong";
    details = void 0;
  }
  res.status(status).json({
    error: true,
    message,
    ...details && { details },
    ...process.env.NODE_ENV === "development" && { stack: err.stack }
  });
}
function notFoundHandler(req, res) {
  res.status(404).json({
    error: true,
    message: "Route not found",
    path: req.originalUrl
  });
}
function requestLogger(req, res, next) {
  const start = Date.now();
  res.on("finish", () => {
    const duration = Date.now() - start;
    const { method, originalUrl, ip } = req;
    const { statusCode } = res;
    console.log(`${method} ${originalUrl} ${statusCode} ${duration}ms - ${ip}`);
  });
  next();
}

// server/index.ts
import connectPgSimple from "connect-pg-simple";
import MemoryStore from "memorystore";
dotenv2.config();
var app = express2();
app.set("trust proxy", 1);
app.use(helmet({
  contentSecurityPolicy: process.env.NODE_ENV === "production" ? {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "blob:", "https://replit.com"],
      connectSrc: ["'self'", "ws:", "wss:", "blob:"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"]
    }
  } : false,
  // Disable CSP in development for easier debugging
  crossOriginEmbedderPolicy: false
}));
var isDevelopment = process.env.NODE_ENV === "development";
var limiter = rateLimit({
  windowMs: 15 * 60 * 1e3,
  // 15 minutesß
  max: isDevelopment ? 1e3 : 500,
  // Higher limit for development
  message: {
    error: "Too many requests from this IP, please try again later."
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    return isDevelopment && (req.ip === "127.0.0.1" || req.ip === "::1" || req.ip === "::ffff:127.0.0.1");
  }
});
var authLimiter = rateLimit({
  windowMs: 15 * 60 * 1e3,
  // 15 minutes
  max: isDevelopment ? 100 : 5,
  // Much higher limit for development
  message: {
    error: "Too many authentication attempts, please try again later."
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    return isDevelopment && (req.ip === "127.0.0.1" || req.ip === "::1" || req.ip === "::ffff:127.0.0.1");
  }
});
if (!isDevelopment) {
  app.use(limiter);
  app.use("/api/auth", authLimiter);
} else {
  console.log("Rate limiting disabled for development environment");
}
app.use(requestLogger);
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
var isDevEnvironment = process.env.NODE_ENV === "development";
var sessionStore;
if (isDevEnvironment) {
  const MemoryStoreConstructor = MemoryStore(session);
  sessionStore = new MemoryStoreConstructor({
    checkPeriod: 864e5
    // prune expired entries every 24h
  });
} else {
  const PgSession = connectPgSimple(session);
  sessionStore = new PgSession({
    pool: pool || void 0,
    // Use the existing database pool
    tableName: "user_sessions",
    // Use a custom table name
    createTableIfMissing: true
  });
}
app.use(session({
  store: sessionStore,
  secret: process.env.SESSION_SECRET || "default-session-secret-change-in-production",
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === "production",
    // Use secure cookies in production
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1e3
    // 24 hours
  }
}));
app.use((req, res, next) => {
  const start = Date.now();
  const path4 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path4.startsWith("/api")) {
      let logLine = `${req.method} ${path4} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use("/api/*", notFoundHandler);
  app.use(errorHandler);
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = process.env.PORT || 5500;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();
